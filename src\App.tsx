import "./App.css";
import { useState, useEffect } from "react";

import { open } from "@tauri-apps/plugin-dialog";
import { readTextFile } from "@tauri-apps/plugin-fs";

function App() {
  const [userAgent, setUserAgent] = useState<string>("");
  const [fileContent, setFileContent] = useState<string>("");

  useEffect(() => {
    const getUserAgent = async () => {
      try {
        const ua = navigator.userAgent;
        setUserAgent(ua);
      } catch (error) {
        console.error("获取 User Agent 时出错:", error);
      }
    };
    getUserAgent();
  }, []);

  const openFile = async () => {
    try {
      // 打开文件对话框，只允许选择.txt文件
      const selected = await open({
        multiple: false,
        filters: [{
          name: "文本文件",
          extensions: ["txt"]
        }]
      });

      // 如果用户选择了文件
      if (selected) {
        // 读取文件内容
        const content = await readTextFile(selected as string);
        setFileContent(content);
      }
    } catch (error) {
      console.error("打开文件时出错:", error);
    }
  };

  return (
    <div className="container">
      <button onClick={openFile}>打开文本文件</button>
      <p>浏览器内核信息: {userAgent}</p>
      {fileContent && (
        <div className="file-content">
          <h3>文件内容：</h3>
          <pre>{fileContent}</pre>
        </div>
      )}
    </div>
  );
}

export default App;
